# Open WebUI 安装和运行指南

本文档介绍如何克隆和运行 [Open WebUI](https://github.com/open-webui/open-webui) 项目。

## 项目简介

Open WebUI 是一个可扩展、功能丰富且用户友好的自托管 WebUI，专为各种 LLM 运行器设计，完全离线运行。

## 系统要求

- Node.js (推荐版本 18 或更高)
- Python 3.11+
- Git
- Docker (可选，用于容器化部署)

## 安装步骤

### 1. 克隆项目

```bash
git clone https://github.com/open-webui/open-webui.git
cd open-webui
```

### 2. 使用 Docker 运行 (推荐)

#### 快速启动

```bash
docker run -d -p 3000:8080 --add-host=host.docker.internal:host-gateway -v open-webui:/app/backend/data --name open-webui --restart always ghcr.io/open-webui/open-webui:main
```

#### 使用 Docker Compose

创建 `docker-compose.yml` 文件：

```yaml
version: '3.8'

services:
  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    container_name: open-webui
    volumes:
      - open-webui:/app/backend/data
    ports:
      - "3000:8080"
    environment:
      - OLLAMA_BASE_URL=http://host.docker.internal:11434
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped

volumes:
  open-webui:
```

运行：

```bash
docker-compose up -d
```

### 3. 本地开发环境安装

#### 后端设置

```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 运行后端
python main.py
```

#### 前端设置

```bash
# 在项目根目录
npm install

# 开发模式运行
npm run dev

# 或者构建生产版本
npm run build
```

## 配置

### 环境变量

创建 `.env` 文件并配置以下变量：

```env
# Ollama 配置
OLLAMA_BASE_URL=http://localhost:11434

# 数据库配置
DATABASE_URL=sqlite:///./webui.db

# 认证配置
WEBUI_SECRET_KEY=your-secret-key-here

# 其他配置
WEBUI_NAME="Open WebUI"
DEFAULT_MODELS=llama2
```

### Ollama 集成

1. 安装 Ollama：
   ```bash
   curl -fsSL https://ollama.ai/install.sh | sh
   ```

2. 启动 Ollama 服务：
   ```bash
   ollama serve
   ```

3. 下载模型：
   ```bash
   ollama pull llama2
   ```

## 访问应用

- 默认访问地址：http://localhost:3000
- 首次访问时需要创建管理员账户

## 功能特性

- 🚀 直观的界面设计
- 📱 响应式设计，支持移动端
- ⚡ 快速响应
- 🔒 安全的用户认证
- 🎨 可自定义主题
- 📚 支持多种 LLM 模型
- 💬 聊天历史记录
- 📁 文件上传支持
- 🔌 插件系统

## 故障排除

### 常见问题

1. **端口冲突**
   - 检查端口 3000 和 8080 是否被占用
   - 修改 docker-compose.yml 中的端口映射

2. **Ollama 连接失败**
   - 确保 Ollama 服务正在运行
   - 检查 OLLAMA_BASE_URL 配置是否正确

3. **权限问题**
   - 确保 Docker 有足够的权限
   - 检查文件夹权限设置

### 日志查看

```bash
# Docker 日志
docker logs open-webui

# Docker Compose 日志
docker-compose logs -f
```

## 更新

### Docker 更新

```bash
docker pull ghcr.io/open-webui/open-webui:main
docker stop open-webui
docker rm open-webui
# 重新运行容器
```

### 本地开发更新

```bash
git pull origin main
pip install -r backend/requirements.txt
npm install
```

## 相关链接

- [Open WebUI GitHub](https://github.com/open-webui/open-webui)
- [Ollama 官网](https://ollama.ai/)
- [Docker 官方文档](https://docs.docker.com/)

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

本项目采用 MIT 许可证。
